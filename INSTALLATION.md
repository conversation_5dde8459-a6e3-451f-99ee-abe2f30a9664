# 🚀 Gemini CLI Chat 安装指南

## 📋 系统要求

-   **Node.js**: 18.0 或更高版本
-   **npm**: 8.0 或更高版本
-   **操作系统**: macOS, Linux, Windows (WSL)
-   **网络**: 需要访问 Google AI API (可能需要代理)

## 📦 安装步骤

### 方法一：从 GitHub 克隆 (推荐)

#### Linux/macOS:

```bash
# 1. 克隆项目
git clone https://github.com/your-username/gemini-cli-chat.git
cd gemini-cli-chat

# 2. 运行自动安装脚本
./setup.sh
```

#### Windows:

```cmd
# 1. 克隆项目
git clone https://github.com/your-username/gemini-cli-chat.git
cd gemini-cli-chat

# 2. 运行自动安装脚本
setup.bat
```

### 方法二：手动安装

#### Linux/macOS:

```bash
# 1. 下载并解压项目
wget https://github.com/your-username/gemini-cli-chat/archive/main.zip
unzip main.zip
cd gemini-cli-chat-main

# 2. 安装依赖
npm install
cd web && npm install && cd ..
cd backend && npm install && cd ..
```

#### Windows:

```cmd
# 1. 下载并解压项目到本地
# 2. 打开命令提示符，进入项目目录
cd gemini-cli-chat-main

# 3. 安装依赖
npm install
cd web && npm install && cd ..
cd backend && npm install && cd ..
```

## 🔐 配置认证

### 步骤 1: 获取 Google Cloud 项目

1. 访问 [Google Cloud Console](https://console.cloud.google.com/)
2. 创建新项目或选择现有项目
3. 记录项目 ID (例如: `my-gemini-project-123456`)

### 步骤 2: 启用 Gemini API

1. 在 Google Cloud Console 中，转到 "APIs & Services" > "Library"
2. 搜索 "Generative Language API" 或 "Gemini API"
3. 点击启用

### 步骤 3: 设置 OAuth 2.0

1. 转到 "APIs & Services" > "Credentials"
2. 点击 "Create Credentials" > "OAuth 2.0 Client IDs"
3. 选择 "Desktop application"
4. 下载 JSON 文件，重命名为 `oauth_creds.json`

### 步骤 4: 配置环境

#### Linux/macOS:

```bash
# 设置项目 ID
export GOOGLE_CLOUD_PROJECT="your-project-id"

# 如果在中国大陆，设置代理 (可选)
export https_proxy=http://127.0.0.1:7890
export http_proxy=http://127.0.0.1:7890
export all_proxy=socks5://127.0.0.1:7890
```

#### Windows:

```cmd
# 设置项目 ID
set GOOGLE_CLOUD_PROJECT=your-project-id

# 如果在中国大陆，设置代理 (可选)
set https_proxy=http://127.0.0.1:7890
set http_proxy=http://127.0.0.1:7890
set all_proxy=socks5://127.0.0.1:7890
```

### 步骤 5: 放置认证文件

#### Linux/macOS:

```bash
# 创建配置目录
mkdir -p ~/.gemini

# 复制 OAuth 凭证文件
cp /path/to/your/oauth_creds.json ~/.gemini/oauth_creds.json
```

#### Windows:

```cmd
# 创建配置目录
mkdir "%USERPROFILE%\.gemini"

# 复制 OAuth 凭证文件
copy "C:\path\to\your\oauth_creds.json" "%USERPROFILE%\.gemini\oauth_creds.json"
```

## 🚀 启动应用

### 快速启动

#### Linux/macOS:

```bash
# 开发模式启动
./start.sh

# 或稳定模式启动 (推荐)
./start-stable.sh
```

#### Windows:

```cmd
# 开发模式启动
start.bat

# 或稳定模式启动 (推荐)
start-stable.bat
```

启动成功后：

-   **前端界面**: http://localhost:9000
-   **后端 API**: http://localhost:8000
-   **Roo Code 兼容 API**: http://localhost:8000/v1

### 手动启动 (调试用)

#### Linux/macOS:

```bash
# 启动后端
cd backend
npm run build
npm start

# 新终端启动前端
cd web
npm run dev
```

#### Windows:

```cmd
# 启动后端
cd backend
npm run build
npm start

# 新命令提示符启动前端
cd web
npm run dev
```

## 🔧 配置选项

### 环境变量

| 变量名                 | 描述                 | 示例                        |
| ---------------------- | -------------------- | --------------------------- |
| `GOOGLE_CLOUD_PROJECT` | Google Cloud 项目 ID | `my-project-123456`         |
| `GEMINI_OAUTH_CREDS`   | OAuth 凭证文件路径   | `/path/to/oauth_creds.json` |
| `https_proxy`          | HTTPS 代理 (可选)    | `http://127.0.0.1:7890`     |
| `http_proxy`           | HTTP 代理 (可选)     | `http://127.0.0.1:7890`     |

### 多账户配置

```bash
# 使用不同的 OAuth 凭证
export GEMINI_OAUTH_CREDS="/path/to/account1/oauth_creds.json"
export GOOGLE_CLOUD_PROJECT="project-1-id"

# 或者使用内置的账户管理
# 访问 http://localhost:9000 的配置面板
```

## 🔌 Roo Code 集成

如果你想在 Roo Code 中使用：

1. 启动 Gemini CLI Chat
2. 在 Roo Code 中配置：
    ```
    API Provider: OpenAI Compatible
    Base URL: http://localhost:8000/v1
    API Key: any-key-works
    Model: gemini-2.5-flash
    ```

## 🐛 故障排除

### 常见问题

#### 1. 认证失败

```bash
# 检查凭证文件
ls -la ~/.gemini/oauth_creds.json

# 检查项目 ID
echo $GOOGLE_CLOUD_PROJECT
```

#### 2. 端口占用

```bash
# 检查端口占用
lsof -i :8000
lsof -i :9000

# 杀死占用进程
kill -9 $(lsof -ti:8000)
```

#### 3. 网络问题 (中国大陆)

```bash
# 设置代理
export https_proxy=http://127.0.0.1:7890
export http_proxy=http://127.0.0.1:7890

# 测试连接
curl -x http://127.0.0.1:7890 https://generativelanguage.googleapis.com
```

#### 4. 依赖安装失败

```bash
# 清理缓存
npm cache clean --force

# 重新安装
rm -rf node_modules package-lock.json
npm install
```

### 日志查看

```bash
# 查看后端日志
cd backend && npm start

# 查看前端日志
cd web && npm run dev
```

## 📚 使用指南

### 基本使用

1. 打开浏览器访问 http://localhost:9000
2. 选择 Gemini 模型 (推荐 `gemini-2.5-flash`)
3. 开始对话

### API 使用

```bash
# 测试 API
curl -X POST http://localhost:8000/gemini-2.5-flash:generateContent \
  -H "Content-Type: application/json" \
  -d '{
    "contents": [
      {
        "role": "user",
        "parts": [{"text": "Hello!"}]
      }
    ]
  }'
```

### OpenAI 兼容 API

```bash
# 测试 OpenAI 兼容接口
curl -X POST http://localhost:8000/v1/chat/completions \
  -H "Content-Type: application/json" \
  -d '{
    "model": "gemini-2.5-flash",
    "messages": [{"role": "user", "content": "Hello!"}]
  }'
```

## 🤝 获取帮助

如果遇到问题：

1. 查看 [故障排除](#故障排除) 部分
2. 检查 [GitHub Issues](https://github.com/your-username/gemini-cli-chat/issues)
3. 提交新的 Issue 描述你的问题

## 📄 许可证

本项目采用 MIT 许可证，详见 [LICENSE](LICENSE) 文件。
