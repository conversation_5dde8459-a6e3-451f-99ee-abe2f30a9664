@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

echo 🚀 Gemini CLI Chat 自动安装脚本 (Windows)
echo ==========================================
echo.

REM 检查系统要求
echo 🔍 检查系统要求...

REM 检查 Node.js
node --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Node.js 未安装
    echo 请先安装 Node.js 18+ : https://nodejs.org/
    pause
    exit /b 1
)

for /f "tokens=1 delims=v" %%i in ('node --version') do set NODE_VERSION=%%i
for /f "tokens=1 delims=." %%i in ("%NODE_VERSION:~1%") do set NODE_MAJOR=%%i
if %NODE_MAJOR% LSS 18 (
    echo ❌ Node.js 版本过低 ^(当前: %NODE_VERSION%, 需要: 18+^)
    echo 请升级 Node.js: https://nodejs.org/
    pause
    exit /b 1
)

echo ✅ Node.js %NODE_VERSION%

REM 检查 npm
npm --version >nul 2>&1
if errorlevel 1 (
    echo ❌ npm 未安装
    pause
    exit /b 1
)

for /f %%i in ('npm --version') do set NPM_VERSION=%%i
echo ✅ npm %NPM_VERSION%
echo.

REM 安装依赖
echo 📦 安装项目依赖...

echo 🔧 安装根目录依赖...
call npm install
if errorlevel 1 (
    echo ❌ 根目录依赖安装失败
    pause
    exit /b 1
)

echo 🔧 安装后端依赖...
cd backend
call npm install
if errorlevel 1 (
    echo ❌ 后端依赖安装失败
    pause
    exit /b 1
)
cd ..

echo 🔧 安装前端依赖...
cd web
call npm install
if errorlevel 1 (
    echo ❌ 前端依赖安装失败
    pause
    exit /b 1
)
cd ..

echo ✅ 依赖安装完成
echo.

REM 创建配置目录
echo 📁 创建配置目录...
if not exist "%USERPROFILE%\.gemini" mkdir "%USERPROFILE%\.gemini"
echo ✅ 配置目录创建完成: %USERPROFILE%\.gemini
echo.

REM 检查认证配置
echo 🔐 检查认证配置...

if exist "%USERPROFILE%\.gemini\oauth_creds.json" (
    echo ✅ 找到现有的 OAuth 凭证文件
) else if defined GOOGLE_APPLICATION_CREDENTIALS (
    echo ✅ 找到 GOOGLE_APPLICATION_CREDENTIALS 环境变量
) else if defined GEMINI_OAUTH_CREDS (
    echo ✅ 找到 GEMINI_OAUTH_CREDS 环境变量
) else (
    echo ⚠️  未找到 OAuth 认证配置
    echo.
    echo 📋 请完成以下配置步骤:
    echo.
    echo 1. 获取 Google Cloud 项目 ID:
    echo    - 访问 https://console.cloud.google.com/
    echo    - 创建或选择项目
    echo    - 记录项目 ID
    echo.
    echo 2. 启用 Gemini API:
    echo    - 在 Google Cloud Console 中搜索 'Generative Language API'
    echo    - 点击启用
    echo.
    echo 3. 创建 OAuth 2.0 凭证:
    echo    - 转到 'APIs ^& Services' ^> 'Credentials'
    echo    - 创建 'OAuth 2.0 Client IDs' ^(Desktop application^)
    echo    - 下载 JSON 文件
    echo.
    echo 4. 配置认证:
    echo    copy "下载的文件路径\oauth_creds.json" "%USERPROFILE%\.gemini\oauth_creds.json"
    echo    set GOOGLE_CLOUD_PROJECT=your-project-id
    echo.
    echo 5. ^(可选^) 如果在中国大陆，设置代理:
    echo    set https_proxy=http://127.0.0.1:7890
    echo    set http_proxy=http://127.0.0.1:7890
    echo.
)

REM 检查项目 ID
if defined GOOGLE_CLOUD_PROJECT (
    echo ✅ 项目 ID: %GOOGLE_CLOUD_PROJECT%
) else if defined GEMINI_PROJECT_ID (
    echo ✅ 项目 ID: %GEMINI_PROJECT_ID%
) else (
    echo ⚠️  未设置项目 ID
    echo 请设置: set GOOGLE_CLOUD_PROJECT=your-project-id
)

echo.
echo 🎉 安装完成！
echo.
echo 📋 下一步:
echo 1. 确保已完成认证配置 ^(见上方提示^)
echo 2. 运行应用: start.bat
echo 3. 访问: http://localhost:9000
echo.
echo 📚 详细文档: 查看 INSTALLATION.md
echo 🐛 遇到问题: 查看 INSTALLATION.md 的故障排除部分
echo.
echo 🚀 准备就绪！运行 'start.bat' 启动应用
echo.
pause
