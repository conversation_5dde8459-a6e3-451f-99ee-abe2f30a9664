# Gemini Code Assist API - Node.js 版本

这是一个基于 Node.js 的 Gemini Code Assist API 服务器，从 Deno 版本移植而来。

## 问题修复

原始的 Deno 版本存在以下问题，已在 Node.js 版本中修复：

1. **AuthType 错误**：Deno 版本使用了不存在的 `AuthType.LOGIN_WITH_GOOGLE_PERSONAL`，已修正为 `AuthType.LOGIN_WITH_GOOGLE`
2. **请求格式问题**：API 请求需要在 `contents` 中包含 `role` 字段
3. **模型兼容性**：推荐使用 `gemini-2.5-pro` 等官方支持的模型

## 安装和运行

### 前置条件

1. 确保已安装 Node.js (版本 18+)
2. 确保已通过 Gemini CLI 进行过身份验证：

    ```bash
    # 如果还没有安装 Gemini CLI
    npm install -g @google/gemini-cli-core

    # 进行身份验证（会打开浏览器）
    gemini auth
    ```

### 启动服务器

```bash
# 安装依赖
npm install

# 启动服务器
node code-assist-api-node.js
```

服务器将在 `http://localhost:8000` 启动。

## API 使用

### 支持的端点

-   `POST /{model}:generateContent` - 生成内容
-   `POST /{model}:streamGenerateContent` - 流式生成内容
-   `POST /{model}:countTokens` - 计算 token 数量
-   `POST /{model}:embedContent` - 生成嵌入向量

### 支持的模型

-   `gemini-2.5-pro` (推荐，默认模型)
-   `gemini-2.5-flash`
-   `gemini-2.0-flash`
-   `gemini-1.5-pro`
-   `gemini-1.5-flash`

### 请求格式

**重要**：所有请求的 `contents` 数组中的每个对象都必须包含 `role` 字段。

#### 生成内容

```bash
curl -X POST "http://localhost:8000/gemini-2.5-pro:generateContent" \
  -H "Content-Type: application/json" \
  -d '{
    "contents": [
      {
        "role": "user",
        "parts": [
          {
            "text": "Hello, how are you?"
          }
        ]
      }
    ]
  }'
```

#### 流式生成内容

```bash
curl -X POST "http://localhost:8000/gemini-2.5-pro:streamGenerateContent" \
  -H "Content-Type: application/json" \
  -d '{
    "contents": [
      {
        "role": "user",
        "parts": [
          {
            "text": "Write a short story"
          }
        ]
      }
    ]
  }'
```

#### 计算 Token 数量

```bash
curl -X POST "http://localhost:8000/gemini-2.5-pro:countTokens" \
  -H "Content-Type: application/json" \
  -d '{
    "contents": [
      {
        "role": "user",
        "parts": [
          {
            "text": "This is a test message"
          }
        ]
      }
    ]
  }'
```

## 测试

运行测试脚本来验证所有功能：

```bash
node test-api.js
```

## 错误排查

### 404 错误 "Requested entity was not found"

这通常是由以下原因造成的：

1. **模型名称错误**：确保使用支持的模型名称
2. **身份验证问题**：确保已通过 `gemini auth` 进行身份验证
3. **缓存的凭据过期**：尝试重新认证

### 400 错误 "Please use a valid role"

确保请求中的每个 `content` 对象都包含 `role` 字段：

```json
{
    "contents": [
        {
            "role": "user", // 必须包含这个字段
            "parts": [
                {
                    "text": "Your message here"
                }
            ]
        }
    ]
}
```

### 400 错误 "Request contains an invalid argument"

检查请求格式是否正确，特别是：

-   `contents` 数组格式
-   `parts` 数组格式
-   模型名称是否正确

## 与 Deno 版本的差异

1. **AuthType**：使用 `LOGIN_WITH_GOOGLE` 而不是 `LOGIN_WITH_GOOGLE_PERSONAL`
2. **HTTP 服务器**：使用 Node.js 的 `http` 模块而不是 Deno.serve
3. **请求处理**：手动解析 JSON 而不是使用 `req.json()`
4. **错误处理**：适配 Node.js 的错误处理模式

## 环境变量

-   `PORT`：服务器端口（默认：8000）
-   `CLI_VERSION`：CLI 版本（默认：0.1.4）

## AI 编辑器集成

### Root Code 集成（OpenAI 兼容）

现在 API 服务器内置了 OpenAI 兼容的端点，无需额外的适配器！

1. **启动服务器**：

    ```bash
    npm start
    ```

2. **在 Root Code 中配置**：

    - 选择 "OpenAI Compatible" 作为 API 提供商
    - Base URL: `http://localhost:8000/v1`
    - API Key: 任意值（不会被验证）
    - Model: `gemini-2.5-pro` 或 `gemini-2.5-flash`

3. **支持的 OpenAI 兼容端点**：
    - `GET /v1/models` - 获取可用模型列表
    - `POST /v1/chat/completions` - 聊天完成（支持流式和非流式）

### Claude Code 集成（Anthropic 兼容）

API 服务器同时支持 Anthropic 兼容的端点，可以直接与 Claude Code 集成！

1. **启动服务器**：

    ```bash
    npm start
    ```

2. **在 Claude Code 中配置**：

    设置环境变量：

    ```bash
    export ANTHROPIC_BASE_URL="http://localhost:8000"
    ```

    或者在 Claude Code 设置中：

    - Base URL: `http://localhost:8000`
    - API Key: 任意值（不会被验证）

3. **支持的 Anthropic 兼容端点**：
    - `POST /v1/messages` - 消息完成（支持流式和非流式）
    - 支持 `system` 指令
    - 支持 Claude 模型名称映射：
        - `claude-3-5-sonnet-20241022` → `gemini-2.5-pro`
        - `claude-3-5-haiku-20241022` → `gemini-2.5-flash`
        - `claude-3-opus-20240229` → `gemini-2.5-pro`

### 使用原始 Gemini API

你也可以继续使用原始的 Gemini API 格式：

```bash
curl -X POST "http://localhost:8000/gemini-2.5-pro:generateContent" \
  -H "Content-Type: application/json" \
  -d '{
    "contents": [
      {
        "role": "user",
        "parts": [{"text": "Hello"}]
      }
    ]
  }'
```

### 方法二：使用 MCP 服务器（高级用户）

1. **安装 MCP SDK**：

    ```bash
    npm install @modelcontextprotocol/sdk
    ```

2. **启动 MCP 服务器**：

    ```bash
    node roo-code-mcp-server.js
    ```

3. **在 Root Code 中配置**：
   将 `roo-code-config.json` 的内容添加到你的 Root Code 设置中：

    - 打开 Root Code 设置
    - 找到 MCP 服务器配置部分
    - 添加以下配置：

    ```json
    {
        "mcpServers": {
            "gemini-code-assist": {
                "command": "node",
                "args": ["./roo-code-mcp-server.js"],
                "env": {
                    "GEMINI_API_BASE_URL": "http://localhost:8000",
                    "GEMINI_DEFAULT_MODEL": "gemini-2.5-pro"
                },
                "alwaysAllow": ["gemini_generate_content", "gemini_stream_content", "gemini_count_tokens", "gemini_embed_content"],
                "disabled": false
            }
        }
    }
    ```

4. **在 Root Code 中使用**：
    ```xml
    <use_mcp_tool>
    <server_name>gemini-code-assist</server_name>
    <tool_name>gemini_generate_content</tool_name>
    <arguments>
    {
      "message": "解释一下这段代码的功能",
      "model": "gemini-2.5-pro",
      "temperature": 0.7
    }
    </arguments>
    </use_mcp_tool>
    ```

### 方法二：使用 OpenAI 兼容适配器

1. **启动适配器**：

    ```bash
    node openai-compatible-adapter.js
    ```

2. **在 Root Code 中配置**：
    - 选择 "OpenAI Compatible" 作为 API 提供商
    - Base URL: `http://localhost:8001/v1`
    - API Key: 任意值（不会被验证）
    - Model: `gemini-2.5-pro` 或 `gemini-2.5-flash`

### 可用的工具和功能

#### MCP 工具：

-   `gemini_generate_content` - 生成内容
-   `gemini_stream_content` - 流式生成内容
-   `gemini_count_tokens` - 计算 token 数量
-   `gemini_embed_content` - 生成嵌入向量

#### 支持的参数：

-   `model` - 模型名称
-   `message` - 消息内容
-   `systemInstruction` - 系统指令
-   `temperature` - 温度参数 (0.0-1.0)
-   `maxOutputTokens` - 最大输出 token 数
-   `topP` - Top-p 采样参数
-   `topK` - Top-k 采样参数

## 注意事项

1. 确保已通过 Gemini CLI 进行身份验证
2. 使用正确的请求格式（包含 `role` 字段）
3. 使用支持的模型名称
4. 服务器启动时会自动启用 CORS
5. MCP 服务器需要先启动你的主 API 服务器
6. OpenAI 适配器也需要先启动你的主 API 服务器
