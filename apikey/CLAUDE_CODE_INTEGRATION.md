# Claude Code 集成指南

## 🚀 快速开始

### 1. 启动 API 服务器

```bash
npm start
```

服务器将在 `http://localhost:8000` 启动，同时提供 Anthropic 兼容的 API 端点。

### 2. 配置 Claude Code

#### 方法一：环境变量（推荐）

在启动 Claude Code 之前设置环境变量：

```bash
export ANTHROPIC_BASE_URL="http://localhost:8000"
export ANTHROPIC_API_KEY="sk-test"  # 任意值，不会被验证
```

然后正常启动 Claude Code。

#### 方法二：Claude Code 设置

1. 打开 Claude Code 设置
2. 找到 API 配置部分
3. 设置：
   - **Base URL**: `http://localhost:8000`
   - **API Key**: 任意值（例如：`sk-test`）

### 3. 开始使用

配置完成后，Claude Code 会自动使用你的本地 Gemini API 服务器进行所有 AI 交互。

## 📋 支持的功能

### Anthropic 兼容端点

- `POST /v1/messages` - 消息完成（支持流式和非流式）

### 模型映射

Claude Code 中的模型会自动映射到对应的 Gemini 模型：

| Claude 模型 | Gemini 模型 | 说明 |
|------------|-------------|------|
| `claude-3-5-sonnet-20241022` | `gemini-2.5-pro` | 最强性能模型 |
| `claude-3-5-haiku-20241022` | `gemini-2.5-flash` | 快速响应模型 |
| `claude-3-opus-20240229` | `gemini-2.5-pro` | 高性能模型 |
| `claude-3-sonnet-20240229` | `gemini-2.5-pro` | 平衡性能模型 |
| `claude-3-haiku-20240307` | `gemini-2.5-flash` | 快速模型 |

### 支持的参数

- `max_tokens` - 最大输出 token 数
- `temperature` - 控制输出随机性 (0.0-1.0)
- `system` - 系统指令
- `stream` - 是否启用流式响应

## 🧪 测试

### 手动测试

你可以使用 curl 测试 Anthropic 兼容端点：

```bash
curl -X POST "http://localhost:8000/v1/messages" \
  -H "Content-Type: application/json" \
  -H "x-api-key: test-key" \
  -H "anthropic-version: 2023-06-01" \
  -d '{
    "model": "claude-3-5-sonnet-20241022",
    "max_tokens": 100,
    "messages": [
      {
        "role": "user",
        "content": "用中文说你好"
      }
    ]
  }'
```

### 自动测试

运行测试脚本验证所有功能：

```bash
npm test
```

测试包括：
- ✅ 原始 Gemini API 端点
- ✅ OpenAI 兼容端点
- ✅ Anthropic 兼容端点
- ✅ 流式响应
- ✅ 系统指令

## 🔧 高级配置

### 环境变量

- `PORT` - 服务器端口（默认：8000）
- `CLI_VERSION` - CLI 版本（默认：0.1.4）

### 自定义端口

如果需要使用其他端口：

```bash
PORT=3000 npm start
```

然后设置：
```bash
export ANTHROPIC_BASE_URL="http://localhost:3000"
```

## ❓ 常见问题

### Q: Claude Code 无法连接到 API
A: 确保 API 服务器正在运行（`npm start`），并且 `ANTHROPIC_BASE_URL` 配置正确。

### Q: 模型响应错误
A: 检查模型名称是否正确，推荐使用 `claude-3-5-sonnet-20241022`。

### Q: 身份验证失败
A: 确保已通过 `gemini auth` 进行身份验证，并且凭据未过期。

### Q: 流式响应不工作
A: 确保 Claude Code 支持流式响应，并且网络连接稳定。

## 🎯 优势

- **无缝集成**：直接替换 Anthropic API，无需修改 Claude Code
- **本地运行**：数据不会发送到第三方服务器
- **高性能**：直接使用 Google Gemini 模型
- **完全兼容**：支持 Anthropic API 格式
- **流式支持**：支持实时响应流
- **系统指令**：支持自定义系统提示

## 📚 更多信息

- [Claude Code 官方文档](https://claude.ai/docs)
- [Anthropic API 文档](https://docs.anthropic.com/claude/reference)
- [Gemini API 文档](https://ai.google.dev/docs)

## 🔄 API 格式对比

### Anthropic 原始格式
```json
{
  "model": "claude-3-5-sonnet-20241022",
  "max_tokens": 100,
  "system": "你是一个助手",
  "messages": [
    {
      "role": "user",
      "content": "Hello"
    }
  ]
}
```

### 转换后的 Gemini 格式
```json
{
  "model": "gemini-2.5-pro",
  "contents": [
    {
      "role": "user",
      "parts": [{"text": "Hello"}]
    }
  ],
  "systemInstruction": {
    "text": "你是一个助手"
  },
  "generationConfig": {
    "maxOutputTokens": 100
  }
}
```

现在你可以在 Claude Code 中无缝使用你的本地 Gemini API 了！🎊
