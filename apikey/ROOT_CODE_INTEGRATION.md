# Root Code 集成指南

## 🚀 快速开始

### 1. 启动 API 服务器

```bash
npm start
```

服务器将在 `http://localhost:8000` 启动，同时提供：
- 原始 Gemini API 端点
- OpenAI 兼容的 API 端点

### 2. 在 Root Code 中配置

1. 打开 Root Code 设置
2. 选择 "OpenAI Compatible" 作为 API 提供商
3. 配置以下参数：
   - **Base URL**: `http://localhost:8000/v1`
   - **API Key**: 任意值（例如：`sk-test`，不会被验证）
   - **Model**: `gemini-2.5-pro` 或 `gemini-2.5-flash`

### 3. 开始使用

配置完成后，Root Code 会自动使用你的本地 Gemini API 服务器进行所有 AI 交互。

## 📋 支持的功能

### OpenAI 兼容端点

- `GET /v1/models` - 获取可用模型列表
- `POST /v1/chat/completions` - 聊天完成（支持流式和非流式）

### 支持的模型

- `gemini-2.5-pro` - 最强性能模型
- `gemini-2.5-flash` - 快速响应模型  
- `gemini-2.0-flash` - 平衡性能模型

### 支持的参数

- `temperature` - 控制输出随机性 (0.0-1.0)
- `max_tokens` - 最大输出 token 数
- `top_p` - Top-p 采样参数
- `stream` - 是否启用流式响应

## 🧪 测试

运行测试脚本验证所有功能：

```bash
npm test
```

测试包括：
- ✅ 原始 Gemini API 端点
- ✅ OpenAI 兼容端点
- ✅ 流式响应
- ✅ Token 计数

## 🔧 高级配置

### 环境变量

- `PORT` - 服务器端口（默认：8000）
- `CLI_VERSION` - CLI 版本（默认：0.1.4）

### 自定义端口

如果需要使用其他端口：

```bash
PORT=3000 npm start
```

然后在 Root Code 中使用 `http://localhost:3000/v1` 作为 Base URL。

## ❓ 常见问题

### Q: Root Code 无法连接到 API
A: 确保 API 服务器正在运行（`npm start`），并且 Base URL 配置正确。

### Q: 模型响应错误
A: 检查模型名称是否正确，推荐使用 `gemini-2.5-pro`。

### Q: 身份验证失败
A: 确保已通过 `gemini auth` 进行身份验证，并且凭据未过期。

## 🎯 优势

- **简单配置**：只需一个服务器，无需额外适配器
- **完全兼容**：支持 OpenAI API 格式，无缝集成 Root Code
- **本地运行**：数据不会发送到第三方服务器
- **高性能**：直接使用 Google Gemini 模型
- **流式支持**：支持实时响应流

## 📚 更多信息

- [Root Code 官方文档](https://docs.roo.dev/)
- [Gemini API 文档](https://ai.google.dev/docs)
- [OpenAI API 兼容性](https://platform.openai.com/docs/api-reference)
